package com.example.stocktip;

import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import java.io.IOException;
import java.util.Calendar;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class StockTipWorker extends Worker {

    private static final int NOTIFICATION_ID = 1;
    private final Context context;

    public StockTipWorker(
            @NonNull Context context,
            @NonNull WorkerParameters params) {
        super(context, params);
        this.context = context;
    }

    @NonNull
    @Override
    public Result doWork() {
        // 检查当前时间是否为早上7点
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        
        // 在实际应用中，可以取消注释下面的代码，只在特定时间执行
        // if (hour != 7) {
        //     return Result.success();
        // }

        try {
            // 请求API获取股票提示
            // 在开发测试阶段，使用硬编码的提示信息
            // 在生产环境中，应该使用fetchStockTip()获取实际数据
            String stockTip = "关注：3\n买入：美KHC, 中600083"; // 测试用硬编码数据
            // String stockTip = fetchStockTip(); // 生产环境使用此行

            if (stockTip != null && !stockTip.isEmpty()) {
                // 检查是否是新的股票提示或者通知未被查看
                if (shouldShowNotification(stockTip)) {
                    // 保存股票提示到SharedPreferences
                    MainActivity.updateStockTip(context, stockTip);

                    // 显示通知
                    showNotification(stockTip);

                    // // 强制播放通知声音（即使设备处于静音模式）
                    // playNotificationSound();
                }
            } else {
                // 清除保存的提示
                MainActivity.updateStockTip(context, "");
            }

            return Result.success();
        } catch (Exception e) {
            e.printStackTrace(); // 记录异常信息，便于调试
            return Result.failure();
        }
    }
    
    // // 强制播放通知声音的方法
    // private void playNotificationSound() {
    //     try {
    //         Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
    //         android.media.Ringtone ringtone = RingtoneManager.getRingtone(context, defaultSoundUri);
    //         if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
    //             ringtone.setVolume(1.0f); // 设置最大音量
    //         }
    //         ringtone.play();
    //     } catch (Exception e) {
    //         e.printStackTrace();
    //     }
    // }

    // 检查是否应该显示通知（新内容或未查看的内容）
    private boolean shouldShowNotification(String newStockTip) {
        SharedPreferences prefs = context.getSharedPreferences(MainActivity.PREFS_NAME, Context.MODE_PRIVATE);
        String currentTip = prefs.getString(MainActivity.STOCK_TIP_KEY, "");
        boolean isViewed = prefs.getBoolean(MainActivity.NOTIFICATION_VIEWED_KEY, true);

        // 如果是新的股票提示内容，或者当前内容未被查看，则显示通知
        return !newStockTip.equals(currentTip) || !isViewed;
    }

    private String fetchStockTip() {
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder()
                .url("http://stock.tingtran.top/stocktip")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                return response.body().string();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    private void showNotification(String stockTip) {
        // 创建一个Intent，点击通知时打开MainActivity
        Intent intent = new Intent(context, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        PendingIntent pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_IMMUTABLE);

        // 获取默认通知声音
        Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);

        // 构建通知
        NotificationCompat.Builder notificationBuilder =
                new NotificationCompat.Builder(context, MainActivity.CHANNEL_ID)
                        .setSmallIcon(R.drawable.ic_notification)
                        .setContentTitle("股票提示")
                        .setContentText(stockTip)
                        .setStyle(new NotificationCompat.BigTextStyle().bigText(stockTip))
                        .setPriority(NotificationCompat.PRIORITY_HIGH) // 设置高优先级
                        .setCategory(NotificationCompat.CATEGORY_ALARM) // 设置为警报类别
                        .setAutoCancel(true)
                        .setSound(defaultSoundUri)
                        .setVibrate(new long[]{0, 500, 250, 500}) // 添加震动模式
                        .setLights(0xFF0000FF, 300, 100) // 添加LED灯效果
                        .setContentIntent(pendingIntent);

        // 显示通知
        NotificationManager notificationManager =
                (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        notificationManager.notify(NOTIFICATION_ID, notificationBuilder.build());
    }
}