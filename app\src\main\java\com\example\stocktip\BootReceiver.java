package com.example.stocktip;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import androidx.work.ExistingPeriodicWorkPolicy;
import androidx.work.PeriodicWorkRequest;
import androidx.work.WorkManager;

import java.util.concurrent.TimeUnit;

public class BootReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        if (Intent.ACTION_BOOT_COMPLETED.equals(intent.getAction())) {
            // 设备启动完成后，重新调度工作任务
            scheduleStockTipWorker(context);
        }
    }

    private void scheduleStockTipWorker(Context context) {
        // 创建周期性工作请求，每15分钟执行一次
        PeriodicWorkRequest stockTipWorkRequest =
                new PeriodicWorkRequest.Builder(StockTipWorker.class, 15, TimeUnit.MINUTES)
                        .build();

        // 将工作请求加入队列
        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                "stockTipWork",
                ExistingPeriodicWorkPolicy.REPLACE,
                stockTipWorkRequest);
    }
}