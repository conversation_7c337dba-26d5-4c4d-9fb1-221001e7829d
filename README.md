# 股票提示应用

这是一个简单的Android应用，每天早上7点会请求股票提示API，并在有返回信息时显示在主窗口中，同时设置角标和发出声音提醒。

## 功能

1. 每天早上7:00请求API (`http://stock.tingtran.top/stocktip`)
2. 如果有返回信息，则：
   - 将信息显示在主窗口中
   - 在应用图标处设置角标提示
   - 发出声音提醒
3. 如果没有返回信息，则重置角标（清空角标）

## 技术实现

- 使用WorkManager实现定时任务
- 使用OkHttp进行网络请求
- 使用NotificationManager实现通知和声音提醒
- 使用SharedPreferences保存股票提示信息

## 构建和运行

1. 使用Android Studio打开项目
2. 构建并运行应用

## 注意事项

- 应用需要网络权限才能正常工作
- 角标功能在不同设备上的支持可能不同